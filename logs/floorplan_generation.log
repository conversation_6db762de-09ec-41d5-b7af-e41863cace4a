2025-08-03 16:23:50,128 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 16:23:50,128 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern...
2025-08-03 16:23:50,953 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 401 Unauthorized"
2025-08-03 16:23:50,954 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 401: {"code":"InvalidApiKey","message":"Invalid API-key provided.","request_id":"878ae9fb-26b5-903d-8f11-9b2d1d575f48"}
2025-08-03 16:32:02,447 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 16:32:02,447 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern...
2025-08-03 16:32:02,625 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 401 Unauthorized"
2025-08-03 16:32:02,627 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 401: {"code":"InvalidApiKey","message":"Invalid API-key provided.","request_id":"5ac860de-323c-9c73-b5d6-0616a0d629a5"}
2025-08-03 16:33:01,479 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 16:33:01,479 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern...
2025-08-03 16:33:36,922 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 401 Unauthorized"
2025-08-03 16:33:36,928 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 401: {"code":"InvalidApiKey","message":"Invalid API-key provided.","request_id":"9764bd36-2040-9ac7-9cb8-ea3877f529f9"}
2025-08-03 16:37:19,523 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 16:37:19,524 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern...
2025-08-03 16:37:28,376 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 16:37:28,376 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern...
2025-08-03 16:37:28,466 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 401 Unauthorized"
2025-08-03 16:37:28,467 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 401: {"code":"InvalidApiKey","message":"Invalid API-key provided.","request_id":"30d45cb5-f8d7-9a98-880e-ed187518d990"}
2025-08-03 16:43:24,075 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 16:43:24,075 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern...
2025-08-03 16:43:25,616 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 401 Unauthorized"
2025-08-03 16:43:25,618 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 401: {"code":"InvalidApiKey","message":"Invalid API-key provided.","request_id":"9aa41ad7-a62c-9505-9f56-22655d887171"}
2025-08-03 16:44:22,937 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 16:44:22,937 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern...
2025-08-03 16:44:25,286 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 200 OK"
2025-08-03 16:44:25,291 - floorplan_invest.text_to_floorplan - INFO - Async task created: 8f8e73e0-8c41-4a91-86a8-cf3edfb5d5c8
2025-08-03 16:44:25,361 - httpx - INFO - HTTP Request: GET https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis/tasks/8f8e73e0-8c41-4a91-86a8-cf3edfb5d5c8 "HTTP/1.1 400 Bad Request"
2025-08-03 16:44:25,362 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 400: {"requestId":"ec3bae01-8184-9a0e-8ca7-fc168cdd370b","code":"InvalidParameter","message":"No static resource api/v1/aigc/text2image/image-synthesis/tasks/8f8e73e0-8c41-4a91-86a8-cf3edfb5d5c8."}
2025-08-03 16:48:08,096 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 16:48:08,096 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern...
2025-08-03 16:48:08,853 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 200 OK"
2025-08-03 16:48:08,858 - floorplan_invest.text_to_floorplan - INFO - Async task created: 73bcd2ab-c4c0-4d93-ae8c-94c37ae5acb2
2025-08-03 16:48:09,403 - httpx - INFO - HTTP Request: GET https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis/tasks/73bcd2ab-c4c0-4d93-ae8c-94c37ae5acb2 "HTTP/1.1 400 Bad Request"
2025-08-03 16:48:09,404 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 400: {"requestId":"de9659f6-edb3-9c41-9b18-250385d1bfbd","code":"InvalidParameter","message":"No static resource api/v1/aigc/text2image/image-synthesis/tasks/73bcd2ab-c4c0-4d93-ae8c-94c37ae5acb2."}
2025-08-03 16:48:41,544 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 16:48:41,545 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern...
2025-08-03 16:48:41,753 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 200 OK"
2025-08-03 16:48:41,756 - floorplan_invest.text_to_floorplan - INFO - Async task created: 61ba1baa-2468-43db-8d13-f856db54a096
2025-08-03 16:48:41,828 - httpx - INFO - HTTP Request: GET https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis/tasks/61ba1baa-2468-43db-8d13-f856db54a096 "HTTP/1.1 400 Bad Request"
2025-08-03 16:48:41,828 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 400: {"requestId":"508a4596-5451-96ed-a5fa-e49503143b92","code":"InvalidParameter","message":"No static resource api/v1/aigc/text2image/image-synthesis/tasks/61ba1baa-2468-43db-8d13-f856db54a096."}
2025-08-03 16:49:03,184 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 16:49:03,185 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern...
2025-08-03 16:49:03,488 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 200 OK"
2025-08-03 16:49:03,494 - floorplan_invest.text_to_floorplan - INFO - Async task created: 453dd6d7-caa5-4cba-a5ef-d3d495daa267
2025-08-03 16:50:03,534 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: Request timed out
2025-08-03 17:01:05,205 - floorplan_invest.text_image_to_floorplan - INFO - Starting interior layout generation for: 2 Bedroom Apartment
2025-08-03 17:01:05,213 - floorplan_invest.text_image_to_floorplan - INFO - Input image validated: 1024x1024, PNG
2025-08-03 17:01:05,214 - floorplan_invest.text_image_to_floorplan - INFO - Generated prompt: Interior floor plan layout for a 2 bedroom apartment. within the given building outline. organizing these spaces: 2 bedrooms, 1 bathroom, 1 kitchen, 1 living room, 1 dining room, 1 balcony, 1 entrance. optimizing the interior layout for functionality and flow. following these principles: separate bedrooms for privacy; master bedroom should be larger; kitchen should connect to dining area; good natural lighting in all rooms; proper ventilation throughout; clear separation between public and private areas. ensuring: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 10.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). respecting the existing building boundary and structural constraints. with proper door placements, circulation paths, and room connections....
2025-08-03 17:01:06,207 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 200 OK"
2025-08-03 17:01:06,211 - floorplan_invest.text_image_to_floorplan - INFO - Async task created: 897e4311-1a81-475c-a208-dfc63dd07f7e
2025-08-03 17:01:07,779 - httpx - INFO - HTTP Request: GET https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis/tasks/897e4311-1a81-475c-a208-dfc63dd07f7e "HTTP/1.1 400 Bad Request"
2025-08-03 17:01:07,781 - floorplan_invest.text_image_to_floorplan - ERROR - Generation failed: API request failed with status 400: {"requestId":"472cff6a-4a67-9cd0-871e-5b8cc800c395","code":"InvalidParameter","message":"No static resource api/v1/aigc/text2image/image-synthesis/tasks/897e4311-1a81-475c-a208-dfc63dd07f7e."}
2025-08-03 17:04:05,347 - floorplan_invest.text_image_to_floorplan - INFO - Starting interior layout generation for: 2 Bedroom Apartment
2025-08-03 17:04:05,356 - floorplan_invest.text_image_to_floorplan - INFO - Input image validated: 1024x1024, PNG
2025-08-03 17:04:05,357 - floorplan_invest.text_image_to_floorplan - INFO - Generated prompt: Interior floor plan layout for a 2 bedroom apartment. within the given building outline. organizing these spaces: 2 bedrooms, 1 bathroom, 1 kitchen, 1 living room, 1 dining room, 1 balcony, 1 entrance. optimizing the interior layout for functionality and flow. following these principles: separate bedrooms for privacy; master bedroom should be larger; kitchen should connect to dining area; good natural lighting in all rooms; proper ventilation throughout; clear separation between public and private areas. ensuring: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 10.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). respecting the existing building boundary and structural constraints. with proper door placements, circulation paths, and room connections....
2025-08-03 17:04:05,576 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 200 OK"
2025-08-03 17:04:05,578 - floorplan_invest.text_image_to_floorplan - INFO - Async task created: bdc2287c-c55b-44b4-9c0b-bb9506b58d4c
2025-08-03 17:04:17,053 - httpx - INFO - HTTP Request: GET https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis/tasks/bdc2287c-c55b-44b4-9c0b-bb9506b58d4c "HTTP/1.1 400 Bad Request"
2025-08-03 17:04:17,055 - floorplan_invest.text_image_to_floorplan - ERROR - Generation failed: API request failed with status 400: {"requestId":"f928b840-1204-90f3-93a1-899cc1a4100f","code":"InvalidParameter","message":"No static resource api/v1/aigc/text2image/image-synthesis/tasks/bdc2287c-c55b-44b4-9c0b-bb9506b58d4c."}
2025-08-03 17:10:58,828 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 17:10:58,829 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Technical architectural blueprint of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern minimalist style with modern minimalist. following these design principles: efficient use of space; good natural lighting; proper ventilation; clear circulation paths; bathroom should be accessible from main living area; bedroom should have privacy from main entrance. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). technical architectural blueprint drawing; black lines on white background; top-down orthographic projection; wall thickness clearly shown with double lines; door and window symbols with proper architectural notation; room labels with dimensions (length × width); area measurements in square meters for each room; dimension lines with measurement annotations; scale indicator clearly visible; construction-ready technical drawing style; suitable for building permits and construction documentation. Blueprint requirements: include precise room dimensions with length and width measurements; show wall thickness using standard architectural line weights; display door swing directions and window opening types; add scale bar or scale notation (e.g., 1:100); label each room with name and square footage; show dimension lines with measurement callouts; use standard architectural symbols for fixtures; suitable for construction permits and real estate documentation....
2025-08-03 17:10:59,932 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 200 OK"
2025-08-03 17:10:59,937 - floorplan_invest.text_to_floorplan - INFO - Async task created: 117fb134-3b57-4b0c-9e1a-14b9cb480287
2025-08-03 17:11:00,510 - httpx - INFO - HTTP Request: GET https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis/tasks/117fb134-3b57-4b0c-9e1a-14b9cb480287 "HTTP/1.1 400 Bad Request"
2025-08-03 17:11:00,512 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 400: {"requestId":"693c6dc1-7166-9cb7-af12-86b0cc888052","code":"InvalidParameter","message":"No static resource api/v1/aigc/text2image/image-synthesis/tasks/117fb134-3b57-4b0c-9e1a-14b9cb480287."}
2025-08-03 17:11:23,983 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 17:11:23,983 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Technical architectural blueprint of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern minimalist style with modern minimalist. following these design principles: efficient use of space; good natural lighting; proper ventilation; clear circulation paths; bathroom should be accessible from main living area; bedroom should have privacy from main entrance. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). technical architectural blueprint drawing; black lines on white background; top-down orthographic projection; wall thickness clearly shown with double lines; door and window symbols with proper architectural notation; room labels with dimensions (length × width); area measurements in square meters for each room; dimension lines with measurement annotations; scale indicator clearly visible; construction-ready technical drawing style; suitable for building permits and construction documentation. Blueprint requirements: include precise room dimensions with length and width measurements; show wall thickness using standard architectural line weights; display door swing directions and window opening types; add scale bar or scale notation (e.g., 1:100); label each room with name and square footage; show dimension lines with measurement callouts; use standard architectural symbols for fixtures; suitable for construction permits and real estate documentation....
2025-08-03 17:11:25,050 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 200 OK"
2025-08-03 17:11:25,054 - floorplan_invest.text_to_floorplan - INFO - Async task created: 3c951bd6-2fed-4ab0-a692-85989db3dc9e
2025-08-03 17:11:25,625 - httpx - INFO - HTTP Request: GET https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis/tasks/3c951bd6-2fed-4ab0-a692-85989db3dc9e "HTTP/1.1 400 Bad Request"
2025-08-03 17:11:25,627 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 400: {"requestId":"f497f5ab-c0a9-907c-a1f5-ad089a708367","code":"InvalidParameter","message":"No static resource api/v1/aigc/text2image/image-synthesis/tasks/3c951bd6-2fed-4ab0-a692-85989db3dc9e."}
2025-08-03 17:16:44,238 - floorplan_invest.text_to_floorplan - INFO - Starting generation for: 1 Bedroom Apartment
2025-08-03 17:16:44,238 - floorplan_invest.text_to_floorplan - INFO - Generated prompt: Technical architectural blueprint of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern minimalist style with modern minimalist. following these design principles: efficient use of space; good natural lighting; proper ventilation; clear circulation paths; bathroom should be accessible from main living area; bedroom should have privacy from main entrance. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). technical architectural blueprint drawing; black lines on white background; top-down orthographic projection; wall thickness clearly shown with double lines; door and window symbols with proper architectural notation; room labels with dimensions (length × width); area measurements in square meters for each room; dimension lines with measurement annotations; scale indicator clearly visible; construction-ready technical drawing style; suitable for building permits and construction documentation. Blueprint requirements: include precise room dimensions with length and width measurements; show wall thickness using standard architectural line weights; display door swing directions and window opening types; add scale bar or scale notation (e.g., 1:100); label each room with name and square footage; show dimension lines with measurement callouts; use standard architectural symbols for fixtures; suitable for construction permits and real estate documentation....
2025-08-03 17:16:46,764 - httpx - INFO - HTTP Request: POST https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis "HTTP/1.1 200 OK"
2025-08-03 17:16:46,773 - floorplan_invest.text_to_floorplan - INFO - Async task created: 3809a23c-fe49-4e50-a77d-5f5893a239b3
2025-08-03 17:16:46,849 - httpx - INFO - HTTP Request: GET https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis/tasks/3809a23c-fe49-4e50-a77d-5f5893a239b3 "HTTP/1.1 400 Bad Request"
2025-08-03 17:16:46,850 - floorplan_invest.text_to_floorplan - ERROR - Generation failed: API request failed with status 400: {"requestId":"5676172f-01f8-9494-9507-7190fe85c9f6","code":"InvalidParameter","message":"No static resource api/v1/aigc/text2image/image-synthesis/tasks/3809a23c-fe49-4e50-a77d-5f5893a239b3."}
