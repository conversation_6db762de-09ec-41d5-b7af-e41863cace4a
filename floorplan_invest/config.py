"""
Configuration system for floor plan generation using Alibaba's AI models.
"""

from typing import Dict, List, Optional, Tuple
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from enum import Enum
import os
from pathlib import Path

# Get the project root directory (where .env file is located)
PROJECT_ROOT = Path(__file__).parent.parent
ENV_FILE_PATH = PROJECT_ROOT / ".env"


class RoomType(str, Enum):
    """Enumeration of supported room types."""
    BEDROOM = "bedroom"
    BATHROOM = "bathroom"
    KITCHEN = "kitchen"
    LIVING_ROOM = "living_room"
    DINING_ROOM = "dining_room"
    STUDY = "study"
    BALCONY = "balcony"
    STORAGE = "storage"
    ENTRANCE = "entrance"
    CORRIDOR = "corridor"


class RoomRequirement(BaseModel):
    """Configuration for a specific room requirement."""
    room_type: RoomType
    count: int = Field(ge=0, description="Number of rooms of this type")
    min_area_sqm: Optional[float] = Field(None, ge=0, description="Minimum area in square meters")
    max_area_sqm: Optional[float] = Field(None, ge=0, description="Maximum area in square meters")
    adjacency_preferences: List[RoomType] = Field(default_factory=list, description="Preferred adjacent room types")
    constraints: List[str] = Field(default_factory=list, description="Specific constraints for this room type")


class FloorPlanConfig(BaseModel):
    """Configuration for a floor plan layout."""
    name: str = Field(description="Name of the floor plan configuration")
    total_area_sqm: Optional[float] = Field(None, ge=0, description="Total floor area in square meters")
    room_requirements: List[RoomRequirement] = Field(description="List of room requirements")
    architectural_constraints: List[str] = Field(default_factory=list, description="General architectural constraints")
    design_style: Optional[str] = Field(None, description="Architectural design style preference")


class APISettings(BaseSettings):
    """API configuration settings."""
    alibaba_api_key: str = Field(
        description="Alibaba Cloud API key",
        alias="ALIBABA_API_KEY"
    )
    alibaba_api_secret: str = Field(
        description="Alibaba Cloud API secret",
        alias="ALIBABA_API_SECRET"
    )
    alibaba_endpoint: str = Field(
        default="https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis",
        description="Alibaba API endpoint",
        alias="ALIBABA_ENDPOINT"
    )
    alibaba_model_name: str = Field(
        default="wanx-v1",
        description="Model name to use",
        alias="ALIBABA_MODEL_NAME"
    )

    model_config = SettingsConfigDict(
        env_file=str(ENV_FILE_PATH),
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore",
        populate_by_name=True
    )


class GenerationSettings(BaseSettings):
    """Image generation settings."""
    output_dir: str = Field(
        default="./outputs",
        description="Output directory for generated images",
        alias="OUTPUT_DIR"
    )
    sample_images_dir: str = Field(
        default="./sample_images",
        description="Directory for sample input images",
        alias="SAMPLE_IMAGES_DIR"
    )
    default_image_size: str = Field(
        default="512x512",
        description="Default image size",
        alias="DEFAULT_IMAGE_SIZE"
    )
    default_steps: int = Field(
        default=20,
        ge=1,
        le=100,
        description="Number of generation steps",
        alias="DEFAULT_STEPS"
    )
    default_guidance_scale: float = Field(
        default=7.5,
        ge=1.0,
        le=20.0,
        description="Guidance scale",
        alias="DEFAULT_GUIDANCE_SCALE"
    )
    max_retries: int = Field(
        default=3,
        ge=1,
        description="Maximum number of retries",
        alias="MAX_RETRIES"
    )
    timeout_seconds: int = Field(
        default=60,
        ge=10,
        description="Request timeout in seconds",
        alias="TIMEOUT_SECONDS"
    )
    log_level: str = Field(
        default="INFO",
        description="Logging level",
        alias="LOG_LEVEL"
    )
    log_file: str = Field(
        default="./logs/floorplan_generation.log",
        description="Log file path",
        alias="LOG_FILE"
    )

    model_config = SettingsConfigDict(
        env_file=str(ENV_FILE_PATH),
        env_file_encoding="utf-8",
        case_sensitive=True,
        extra="ignore",
        populate_by_name=True
    )


# Predefined floor plan configurations
PREDEFINED_CONFIGS = {
    "1br_apartment": FloorPlanConfig(
        name="1 Bedroom Apartment",
        total_area_sqm=50.0,
        room_requirements=[
            RoomRequirement(room_type=RoomType.BEDROOM, count=1, min_area_sqm=12.0),
            RoomRequirement(room_type=RoomType.BATHROOM, count=1, min_area_sqm=4.0, 
                          constraints=["must contain only one toilet", "include shower or bathtub"]),
            RoomRequirement(room_type=RoomType.KITCHEN, count=1, min_area_sqm=6.0,
                          constraints=["open or semi-open to living area"]),
            RoomRequirement(room_type=RoomType.LIVING_ROOM, count=1, min_area_sqm=15.0,
                          adjacency_preferences=[RoomType.KITCHEN, RoomType.BALCONY]),
            RoomRequirement(room_type=RoomType.BALCONY, count=1, min_area_sqm=3.0,
                          adjacency_preferences=[RoomType.LIVING_ROOM]),
            RoomRequirement(room_type=RoomType.ENTRANCE, count=1, min_area_sqm=2.0),
        ],
        architectural_constraints=[
            "efficient use of space",
            "good natural lighting",
            "proper ventilation",
            "clear circulation paths",
            "bathroom should be accessible from main living area",
            "bedroom should have privacy from main entrance"
        ],
        design_style="modern minimalist"
    ),
    
    "2br_apartment": FloorPlanConfig(
        name="2 Bedroom Apartment",
        total_area_sqm=80.0,
        room_requirements=[
            RoomRequirement(room_type=RoomType.BEDROOM, count=2, min_area_sqm=10.0),
            RoomRequirement(room_type=RoomType.BATHROOM, count=1, min_area_sqm=5.0,
                          constraints=["must contain only one toilet", "include shower or bathtub"]),
            RoomRequirement(room_type=RoomType.KITCHEN, count=1, min_area_sqm=8.0),
            RoomRequirement(room_type=RoomType.LIVING_ROOM, count=1, min_area_sqm=20.0,
                          adjacency_preferences=[RoomType.KITCHEN, RoomType.DINING_ROOM]),
            RoomRequirement(room_type=RoomType.DINING_ROOM, count=1, min_area_sqm=8.0,
                          adjacency_preferences=[RoomType.KITCHEN, RoomType.LIVING_ROOM]),
            RoomRequirement(room_type=RoomType.BALCONY, count=1, min_area_sqm=4.0),
            RoomRequirement(room_type=RoomType.ENTRANCE, count=1, min_area_sqm=3.0),
        ],
        architectural_constraints=[
            "separate bedrooms for privacy",
            "master bedroom should be larger",
            "kitchen should connect to dining area",
            "good natural lighting in all rooms",
            "proper ventilation throughout",
            "clear separation between public and private areas"
        ],
        design_style="contemporary"
    ),
    
    "3br_house": FloorPlanConfig(
        name="3 Bedroom House",
        total_area_sqm=120.0,
        room_requirements=[
            RoomRequirement(room_type=RoomType.BEDROOM, count=3, min_area_sqm=12.0),
            RoomRequirement(room_type=RoomType.BATHROOM, count=2, min_area_sqm=5.0,
                          constraints=["each bathroom must contain only one toilet"]),
            RoomRequirement(room_type=RoomType.KITCHEN, count=1, min_area_sqm=12.0),
            RoomRequirement(room_type=RoomType.LIVING_ROOM, count=1, min_area_sqm=25.0),
            RoomRequirement(room_type=RoomType.DINING_ROOM, count=1, min_area_sqm=12.0),
            RoomRequirement(room_type=RoomType.STUDY, count=1, min_area_sqm=8.0),
            RoomRequirement(room_type=RoomType.STORAGE, count=1, min_area_sqm=3.0),
            RoomRequirement(room_type=RoomType.ENTRANCE, count=1, min_area_sqm=4.0),
        ],
        architectural_constraints=[
            "master bedroom should have en-suite bathroom",
            "family bathroom accessible from hallway",
            "kitchen should have good workflow triangle",
            "living and dining areas should flow together",
            "study should be quiet and separate from main living areas",
            "good storage throughout the house",
            "proper circulation and hallway space"
        ],
        design_style="traditional family home"
    )
}


def get_config(config_name: str) -> FloorPlanConfig:
    """Get a predefined floor plan configuration by name."""
    if config_name not in PREDEFINED_CONFIGS:
        available = ", ".join(PREDEFINED_CONFIGS.keys())
        raise ValueError(f"Unknown configuration '{config_name}'. Available: {available}")
    return PREDEFINED_CONFIGS[config_name]


def list_available_configs() -> List[str]:
    """List all available predefined configurations."""
    return list(PREDEFINED_CONFIGS.keys())


def create_custom_config(
    name: str,
    room_requirements: List[RoomRequirement],
    total_area_sqm: Optional[float] = None,
    architectural_constraints: Optional[List[str]] = None,
    design_style: Optional[str] = None
) -> FloorPlanConfig:
    """Create a custom floor plan configuration."""
    return FloorPlanConfig(
        name=name,
        total_area_sqm=total_area_sqm,
        room_requirements=room_requirements,
        architectural_constraints=architectural_constraints or [],
        design_style=design_style
    )


# Initialize settings
def get_api_settings() -> APISettings:
    """Get API settings from environment."""
    return APISettings()


def get_generation_settings() -> GenerationSettings:
    """Get generation settings from environment."""
    return GenerationSettings()
