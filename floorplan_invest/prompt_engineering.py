"""
Prompt engineering utilities for floor plan generation.
"""

from typing import List, Dict, Optional, Tuple
from .config import FloorPlanConfig, RoomRequirement, RoomType


class FloorPlanPromptBuilder:
    """Builder class for creating comprehensive floor plan generation prompts."""
    
    def __init__(self):
        """Initialize the prompt builder."""
        self.base_architectural_principles = [
            "Follow proper architectural design principles",
            "Ensure efficient space utilization",
            "Maintain proper circulation paths and corridors",
            "Provide adequate natural lighting and ventilation",
            "Consider structural integrity and load-bearing elements",
            "Include proper door and window placements",
            "Ensure accessibility and safety compliance",
        ]
        
        self.room_specific_constraints = {
            RoomType.BATHROOM: [
                "Each bathroom must contain only one toilet",
                "Include proper ventilation (window or exhaust fan)",
                "Ensure waterproofing and drainage",
                "Provide adequate space for fixtures",
                "Consider privacy and accessibility",
            ],
            RoomType.KITCHEN: [
                "Design efficient work triangle (sink, stove, refrigerator)",
                "Provide adequate counter space and storage",
                "Ensure proper ventilation for cooking",
                "Include electrical outlets for appliances",
                "Consider natural lighting for food preparation",
            ],
            RoomType.BEDROOM: [
                "Ensure privacy from main living areas",
                "Provide adequate space for bed and furniture",
                "Include closet or storage space",
                "Ensure natural lighting and ventilation",
                "Consider noise isolation",
            ],
            RoomType.LIVING_ROOM: [
                "Create open and welcoming space",
                "Ensure good natural lighting",
                "Provide flexible furniture arrangement options",
                "Consider entertainment and social functions",
                "Connect well with other public spaces",
            ],
        }
        
        self.style_modifiers = {
            "modern": "clean lines, minimalist design, open floor plan concepts",
            "contemporary": "current design trends, mixed materials, flexible spaces",
            "traditional": "classic proportions, defined room boundaries, formal layouts",
            "minimalist": "simple forms, maximum efficiency, reduced visual clutter",
            "family-friendly": "practical layouts, safe circulation, child-friendly spaces",
        }
    
    def build_text_to_image_prompt(self, config: FloorPlanConfig, 
                                  include_technical_specs: bool = True) -> str:
        """Build a comprehensive text-to-image prompt for floor plan generation."""
        prompt_parts = []
        
        # Base description - emphasize technical blueprint nature
        prompt_parts.append(f"Technical architectural blueprint of a {config.name.lower()}")
        
        # Total area if specified
        if config.total_area_sqm:
            prompt_parts.append(f"with total area of {config.total_area_sqm} square meters")
        
        # Room requirements
        room_descriptions = self._build_room_descriptions(config.room_requirements)
        if room_descriptions:
            prompt_parts.append(f"containing: {room_descriptions}")
        
        # Design style
        if config.design_style:
            style_desc = self.style_modifiers.get(config.design_style.lower(), config.design_style)
            prompt_parts.append(f"designed in {config.design_style} style with {style_desc}")
        
        # Architectural constraints
        if config.architectural_constraints:
            constraints_text = self._format_constraints(config.architectural_constraints)
            prompt_parts.append(f"following these design principles: {constraints_text}")
        
        # Room-specific constraints
        room_constraints = self._build_room_constraints(config.room_requirements)
        if room_constraints:
            prompt_parts.append(f"with specific room requirements: {room_constraints}")
        
        # Technical specifications
        if include_technical_specs:
            tech_specs = self._build_technical_specifications()
            prompt_parts.append(tech_specs)

            # Add blueprint-specific requirements
            blueprint_reqs = self._build_blueprint_requirements()
            prompt_parts.append(f"Blueprint requirements: {blueprint_reqs}")
        
        # Join all parts
        prompt = ". ".join(prompt_parts) + "."
        
        return self._clean_prompt(prompt)
    
    def build_image_to_image_prompt(self, config: FloorPlanConfig, 
                                   boundary_description: str = "given building outline") -> str:
        """Build a prompt for image-to-image generation (interior layout within boundary)."""
        prompt_parts = []
        
        # Base description - emphasize technical blueprint nature
        prompt_parts.append(f"Technical architectural blueprint layout for a {config.name.lower()}")
        prompt_parts.append(f"within the {boundary_description}")
        
        # Room requirements
        room_descriptions = self._build_room_descriptions(config.room_requirements)
        if room_descriptions:
            prompt_parts.append(f"organizing these spaces: {room_descriptions}")
        
        # Layout optimization
        prompt_parts.append("optimizing the interior layout for functionality and flow")
        
        # Design constraints
        if config.architectural_constraints:
            constraints_text = self._format_constraints(config.architectural_constraints)
            prompt_parts.append(f"following these principles: {constraints_text}")
        
        # Room-specific requirements
        room_constraints = self._build_room_constraints(config.room_requirements)
        if room_constraints:
            prompt_parts.append(f"ensuring: {room_constraints}")
        
        # Boundary respect
        prompt_parts.append("respecting the existing building boundary and structural constraints")
        
        # Technical requirements
        prompt_parts.append("with proper door placements, circulation paths, and room connections")
        
        prompt = ". ".join(prompt_parts) + "."
        
        return self._clean_prompt(prompt)

    def build_construction_blueprint_prompt(self, config: FloorPlanConfig) -> str:
        """Build a prompt specifically optimized for construction-ready blueprints."""
        prompt_parts = []

        # Emphasize technical blueprint nature from the start
        prompt_parts.append(f"Professional architectural construction blueprint of a {config.name.lower()}")

        # Total area with emphasis on technical accuracy
        if config.total_area_sqm:
            prompt_parts.append(f"total floor area: {config.total_area_sqm} square meters")

        # Room requirements with technical focus
        room_descriptions = self._build_room_descriptions(config.room_requirements)
        if room_descriptions:
            prompt_parts.append(f"containing: {room_descriptions}")

        # Technical drawing specifications
        tech_specs = self._build_technical_specifications()
        prompt_parts.append(tech_specs)

        # Blueprint requirements
        blueprint_reqs = self._build_blueprint_requirements()
        prompt_parts.append(blueprint_reqs)

        # Emphasize construction readiness
        prompt_parts.append("ready for construction permits, building approval, and contractor use")
        prompt_parts.append("NOT an interior design rendering or decorative visualization")

        prompt = ". ".join(prompt_parts) + "."
        return self._clean_prompt(prompt)

    def build_negative_prompt(self) -> str:
        """Build a negative prompt to avoid common issues and interior design elements."""
        negative_elements = [
            "blurry", "low quality", "distorted", "incomplete",
            "multiple toilets in one bathroom", "overlapping rooms",
            "impossible layouts", "structural impossibilities",
            "missing doors", "inaccessible rooms", "poor circulation",
            "unrealistic proportions", "cluttered", "confusing layout",
            "interior design rendering", "3D visualization", "perspective view",
            "furniture", "decorations", "interior styling", "color schemes",
            "artistic rendering", "decorative elements", "aesthetic visualization",
            "isometric view", "rendered materials", "lighting effects",
            "shadows", "textures", "photorealistic", "watermarks"
        ]

        return ", ".join(negative_elements)
    
    def _build_room_descriptions(self, room_requirements: List[RoomRequirement]) -> str:
        """Build descriptions of room requirements."""
        room_counts = {}
        for req in room_requirements:
            room_type = req.room_type.value.replace("_", " ")
            if req.count > 1:
                room_counts[room_type] = f"{req.count} {room_type}s"
            elif req.count == 1:
                room_counts[room_type] = f"1 {room_type}"
        
        return ", ".join(room_counts.values())
    
    def _build_room_constraints(self, room_requirements: List[RoomRequirement]) -> str:
        """Build room-specific constraints."""
        constraints = []
        
        for req in room_requirements:
            # Add default constraints for room type
            if req.room_type in self.room_specific_constraints:
                constraints.extend(self.room_specific_constraints[req.room_type])
            
            # Add custom constraints
            constraints.extend(req.constraints)
            
            # Add area constraints
            if req.min_area_sqm:
                room_name = req.room_type.value.replace("_", " ")
                constraints.append(f"{room_name} minimum {req.min_area_sqm}m²")
        
        return "; ".join(constraints[:8])  # Limit to avoid overly long prompts
    
    def _format_constraints(self, constraints: List[str]) -> str:
        """Format architectural constraints."""
        return "; ".join(constraints[:6])  # Limit to avoid overly long prompts
    
    def _build_technical_specifications(self) -> str:
        """Build technical drawing specifications for architectural blueprints."""
        specs = [
            "technical architectural blueprint drawing",
            "black lines on white background",
            "top-down orthographic projection",
            "wall thickness clearly shown with double lines",
            "door and window symbols with proper architectural notation",
            "room labels with dimensions (length × width)",
            "area measurements in square meters for each room",
            "dimension lines with measurement annotations",
            "scale indicator clearly visible",
            "construction-ready technical drawing style",
            "suitable for building permits and construction documentation"
        ]
        return "; ".join(specs)

    def _build_blueprint_requirements(self) -> str:
        """Build specific requirements for construction-ready blueprints."""
        requirements = [
            "include precise room dimensions with length and width measurements",
            "show wall thickness using standard architectural line weights",
            "display door swing directions and window opening types",
            "add scale bar or scale notation (e.g., 1:100)",
            "label each room with name and square footage",
            "show dimension lines with measurement callouts",
            "use standard architectural symbols for fixtures",
            "suitable for construction permits and real estate documentation"
        ]
        return "; ".join(requirements)

    def _clean_prompt(self, prompt: str) -> str:
        """Clean and optimize the prompt."""
        # Remove redundant spaces and punctuation
        prompt = " ".join(prompt.split())
        prompt = prompt.replace(" .", ".").replace(" ,", ",").replace(" ;", ";")
        
        # Ensure proper sentence structure
        if not prompt.endswith("."):
            prompt += "."
        
        return prompt


class PromptVariationGenerator:
    """Generate variations of prompts for testing different approaches."""
    
    def __init__(self, base_builder: FloorPlanPromptBuilder):
        """Initialize with a base prompt builder."""
        self.base_builder = base_builder
    
    def generate_variations(self, config: FloorPlanConfig, num_variations: int = 3) -> List[Dict[str, str]]:
        """Generate multiple prompt variations for testing."""
        variations = []
        
        for i in range(num_variations):
            variation = {
                "name": f"{config.name}_variation_{i+1}",
                "prompt": self._create_variation(config, i),
                "negative_prompt": self.base_builder.build_negative_prompt(),
                "description": self._get_variation_description(i)
            }
            variations.append(variation)
        
        return variations
    
    def _create_variation(self, config: FloorPlanConfig, variation_index: int) -> str:
        """Create a specific variation of the prompt."""
        if variation_index == 0:
            # Detailed technical variation
            return self.base_builder.build_text_to_image_prompt(config, include_technical_specs=True)
        elif variation_index == 1:
            # Simplified aesthetic variation
            return self.base_builder.build_text_to_image_prompt(config, include_technical_specs=False)
        else:
            # Custom variation with emphasis on specific aspects
            prompt = self.base_builder.build_text_to_image_prompt(config, include_technical_specs=True)
            prompt += " Emphasize spatial efficiency and modern design aesthetics."
            return prompt
    
    def _get_variation_description(self, variation_index: int) -> str:
        """Get description of what each variation emphasizes."""
        descriptions = [
            "Technical detailed version with full specifications",
            "Simplified aesthetic version focusing on design",
            "Enhanced version with efficiency emphasis"
        ]
        return descriptions[variation_index] if variation_index < len(descriptions) else "Custom variation"


def create_prompt_for_config(config_name: str, config: FloorPlanConfig,
                           prompt_type: str = "text_to_image") -> Dict[str, str]:
    """Convenience function to create prompts for a configuration."""
    builder = FloorPlanPromptBuilder()

    if prompt_type == "text_to_image":
        prompt = builder.build_text_to_image_prompt(config)
    elif prompt_type == "image_to_image":
        prompt = builder.build_image_to_image_prompt(config)
    elif prompt_type == "construction_blueprint":
        prompt = builder.build_construction_blueprint_prompt(config)
    else:
        raise ValueError(f"Unknown prompt type: {prompt_type}")

    return {
        "config_name": config_name,
        "prompt": prompt,
        "negative_prompt": builder.build_negative_prompt(),
        "prompt_type": prompt_type
    }
