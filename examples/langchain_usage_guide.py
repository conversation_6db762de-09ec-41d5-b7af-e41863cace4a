#!/usr/bin/env python3
"""
LangChain-based Alibaba API Client Usage Guide

This script demonstrates how to use the new LangChain-based Alibaba DashScope client
for floor plan generation, showcasing all the enhanced features and capabilities.
"""

import sys
import asyncio
from pathlib import Path
from typing import Dict, Any, List

# Add the parent directory to the path so we can import the package
sys.path.insert(0, str(Path(__file__).parent.parent))

from floorplan_invest.api_client import AlibabaDashScopeImageGenerator, AlibabaAPIClient
from floorplan_invest.config import get_config, get_api_settings, get_generation_settings
from langchain_core.callbacks import BaseCallbackHandler


class DetailedFloorPlanCallback(BaseCallbackHandler):
    """Enhanced callback handler with detailed monitoring."""
    
    def __init__(self):
        self.generation_steps = []
        self.timing_info = {}
        self.error_count = 0
        self.success_count = 0
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: List[str], **kwargs) -> None:
        """Track generation start."""
        import time
        self.timing_info['start'] = time.time()
        print(f"🚀 Starting generation for {len(prompts)} prompt(s)")
        
        for i, prompt in enumerate(prompts):
            print(f"   📝 Prompt {i+1}: {prompt[:80]}...")
    
    def on_llm_end(self, response, **kwargs) -> None:
        """Track successful completion."""
        import time
        self.timing_info['end'] = time.time()
        duration = self.timing_info['end'] - self.timing_info['start']
        
        self.success_count += len(response.generations)
        print(f"✅ Generation completed in {duration:.2f}s")
        print(f"📊 Success rate: {self.success_count}/{self.success_count + self.error_count}")
    
    def on_llm_error(self, error: Exception, **kwargs) -> None:
        """Track errors."""
        self.error_count += 1
        print(f"❌ Generation error: {error}")
    
    def on_text(self, text: str, **kwargs) -> None:
        """Track detailed steps."""
        print(f"   🔄 {text}")
        self.generation_steps.append({
            'timestamp': __import__('time').time(),
            'message': text
        })
    
    def get_summary(self) -> Dict[str, Any]:
        """Get detailed summary of the generation process."""
        return {
            'total_steps': len(self.generation_steps),
            'success_count': self.success_count,
            'error_count': self.error_count,
            'duration': self.timing_info.get('end', 0) - self.timing_info.get('start', 0),
            'steps': self.generation_steps
        }


def example_1_basic_langchain_usage():
    """Example 1: Basic LangChain client usage."""
    print("=" * 60)
    print("EXAMPLE 1: Basic LangChain Client Usage")
    print("=" * 60)
    
    try:
        # Initialize with environment variables (recommended)
        print("\n🔧 Initializing LangChain client...")
        client = AlibabaDashScopeImageGenerator(
            alibaba_api_key="demo_key",  # In real usage, this comes from .env
            alibaba_api_secret="demo_secret",
            default_image_size="512x512",
            max_retries=3,
            timeout_seconds=60
        )
        
        print("✅ Client initialized successfully!")
        print(f"   • Model: {client.model_name}")
        print(f"   • Endpoint: {client.alibaba_endpoint}")
        print(f"   • Image Size: {client.default_image_size}")
        print(f"   • Max Retries: {client.max_retries}")
        
        # Demonstrate LangChain interface
        print("\n🎯 LangChain Standard Interface:")
        prompts = ["Modern 1-bedroom apartment floor plan"]
        
        print(f"   • Calling _generate() with prompts: {prompts}")
        print("   • This follows LangChain's BaseLanguageModel pattern")
        print("   • Would return LLMResult with Generation objects")
        
    except Exception as e:
        print(f"   ⚠️  Expected error (demo credentials): {e}")


def example_2_callback_system():
    """Example 2: Using the callback system for monitoring."""
    print("\n" + "=" * 60)
    print("EXAMPLE 2: Callback System for Monitoring")
    print("=" * 60)
    
    print("\n📊 Setting up detailed monitoring...")
    
    # Create callback handler
    callback = DetailedFloorPlanCallback()
    
    try:
        # Initialize client with callback
        client = AlibabaDashScopeImageGenerator(
            alibaba_api_key="demo_key",
            alibaba_api_secret="demo_secret",
            callbacks=[callback]  # Add callback for monitoring
        )
        
        print("✅ Client with callback initialized!")
        
        # Simulate generation process
        print("\n🎯 Simulating generation with callbacks...")
        config = get_config("1br_apartment")
        prompt = f"Architectural floor plan for {config.name}"
        
        print(f"   • Prompt: {prompt}")
        print("   • Callbacks will track:")
        print("     - Generation start/end times")
        print("     - Individual processing steps")
        print("     - Success/error rates")
        print("     - Detailed timing information")
        
        # Show what the callback would capture
        callback.on_text("Making POST request to API")
        callback.on_text("Request successful: task_12345")
        callback.on_text("Waiting for task completion")
        callback.on_text("Task completed successfully")
        callback.on_text("Downloading image")
        callback.on_text("Image saved successfully")
        
        summary = callback.get_summary()
        print(f"\n📈 Callback Summary:")
        print(f"   • Total Steps: {summary['total_steps']}")
        print(f"   • Success Count: {summary['success_count']}")
        print(f"   • Error Count: {summary['error_count']}")
        
    except Exception as e:
        print(f"   ⚠️  Expected error: {e}")


def example_3_async_capabilities():
    """Example 3: Async capabilities for better performance."""
    print("\n" + "=" * 60)
    print("EXAMPLE 3: Async Capabilities")
    print("=" * 60)
    
    print("\n⚡ Async Features:")
    print("• Built-in async/await support")
    print("• Better concurrency for multiple requests")
    print("• Non-blocking I/O operations")
    print("• Improved performance for batch processing")
    
    print("\n🔧 Async Usage Pattern:")
    print("""
async def generate_multiple_floor_plans():
    client = AlibabaDashScopeImageGenerator(...)
    
    # Multiple concurrent requests
    tasks = []
    for config_name in ["1br_apartment", "2br_apartment", "3br_house"]:
        config = get_config(config_name)
        prompt = f"Floor plan for {config.name}"
        
        # Create async task
        task = client._make_async_request(
            "POST", 
            client.alibaba_endpoint, 
            {"prompt": prompt}
        )
        tasks.append(task)
    
    # Wait for all to complete concurrently
    results = await asyncio.gather(*tasks)
    return results

# Run async function
results = asyncio.run(generate_multiple_floor_plans())
    """)


def example_4_error_handling_and_retries():
    """Example 4: Enhanced error handling and retry mechanisms."""
    print("\n" + "=" * 60)
    print("EXAMPLE 4: Error Handling & Retry Mechanisms")
    print("=" * 60)
    
    print("\n🛡️ Enhanced Error Handling:")
    print("• Tenacity-based retry decorators")
    print("• Exponential backoff with jitter")
    print("• Specific exception handling")
    print("• Configurable retry policies")
    
    print("\n🔄 Retry Configuration:")
    print("""
@retry(
    retry=retry_if_exception_type((httpx.HTTPError, httpx.TimeoutException)),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    reraise=True,
)
async def _make_async_request(self, ...):
    # Automatic retry on specific exceptions
    # Exponential backoff: 4s, 8s, 10s
    # Reraise final exception if all retries fail
    pass
    """)
    
    print("\n📊 Error Types Handled:")
    print("• HTTP status errors (4xx, 5xx)")
    print("• Network timeouts")
    print("• Connection errors")
    print("• Rate limiting (429)")
    print("• Service unavailable (503)")


def example_5_configuration_validation():
    """Example 5: Pydantic configuration validation."""
    print("\n" + "=" * 60)
    print("EXAMPLE 5: Configuration Validation")
    print("=" * 60)
    
    print("\n🛡️ Pydantic Validation Features:")
    print("• Automatic type checking")
    print("• Environment variable integration")
    print("• Field validation and constraints")
    print("• Clear error messages")
    
    print("\n🔧 Configuration Example:")
    print("""
class AlibabaDashScopeImageGenerator(BaseLanguageModel):
    # Validated fields with constraints
    alibaba_api_key: str = Field(..., description="API key")
    default_steps: int = Field(default=20, ge=1, le=100)
    default_guidance_scale: float = Field(default=7.5, ge=1.0, le=20.0)
    max_retries: int = Field(default=3, ge=1)
    
    @model_validator(mode='before')
    @classmethod
    def validate_environment(cls, values):
        # Automatic environment variable loading
        values["alibaba_api_key"] = get_from_dict_or_env(
            values, "alibaba_api_key", "ALIBABA_API_KEY"
        )
        return values
    """)
    
    try:
        # Demonstrate validation
        print("\n✅ Valid Configuration:")
        client = AlibabaDashScopeImageGenerator(
            alibaba_api_key="valid_key",
            alibaba_api_secret="valid_secret",
            default_steps=25,  # Valid: between 1-100
            default_guidance_scale=8.0  # Valid: between 1.0-20.0
        )
        print(f"   • Steps: {client.default_steps}")
        print(f"   • Guidance Scale: {client.default_guidance_scale}")
        
    except Exception as e:
        print(f"   ❌ Validation Error: {e}")


def example_6_backward_compatibility():
    """Example 6: Backward compatibility wrapper."""
    print("\n" + "=" * 60)
    print("EXAMPLE 6: Backward Compatibility")
    print("=" * 60)
    
    print("\n🔄 Migration Strategy:")
    print("• AlibabaAPIClient wrapper maintains original interface")
    print("• Existing code works without changes")
    print("• Gradual migration to LangChain patterns")
    print("• Side-by-side usage during transition")
    
    try:
        # Original interface (still works)
        print("\n📝 Original Interface:")
        api_settings = get_api_settings()
        generation_settings = get_generation_settings()
        
        # This would work with real credentials
        print("   old_client = AlibabaAPIClient(api_settings, generation_settings)")
        print("   result = old_client.text_to_image('floor plan prompt')")
        
        print("\n🚀 New LangChain Interface:")
        print("   new_client = AlibabaDashScopeImageGenerator(...)")
        print("   result = new_client._generate(['floor plan prompt'])")
        
        print("\n✅ Both interfaces available during migration!")
        
    except Exception as e:
        print(f"   ⚠️  Expected error (no credentials): {e}")


def main():
    """Run all usage examples."""
    print("🏗️  LangChain-based Alibaba API Client Usage Guide")
    print("=" * 80)
    
    example_1_basic_langchain_usage()
    example_2_callback_system()
    example_3_async_capabilities()
    example_4_error_handling_and_retries()
    example_5_configuration_validation()
    example_6_backward_compatibility()
    
    print("\n" + "=" * 80)
    print("🎯 KEY TAKEAWAYS")
    print("=" * 80)
    
    takeaways = [
        "🔧 Use AlibabaDashScopeImageGenerator for new implementations",
        "📊 Add callback handlers for monitoring and debugging",
        "⚡ Leverage async capabilities for better performance",
        "🛡️ Benefit from automatic retry and error handling",
        "🌍 Use environment variables for configuration",
        "🔄 Maintain backward compatibility during migration",
        "🧪 Easier testing with LangChain patterns",
        "📈 Better scalability for production use"
    ]
    
    for takeaway in takeaways:
        print(f"  {takeaway}")
    
    print("\n💡 Ready to use the LangChain-based client!")


if __name__ == "__main__":
    main()
