#!/usr/bin/env python3
"""
Basic usage examples for the floor plan generation framework.

This script demonstrates how to use the framework for various floor plan generation tasks.
"""

import sys
import os
from pathlib import Path

# Add the parent directory to the path so we can import the package
sys.path.insert(0, str(Path(__file__).parent.parent))

from floorplan_invest.config import get_config, list_available_configs
from floorplan_invest.text_to_floorplan import FloorPlanGenerator
from floorplan_invest.text_image_to_floorplan import InteriorLayoutGenerator
from floorplan_invest.prompt_engineering import FloorPlanPromptBuilder, PromptVariationGenerator


def example_1_list_configurations():
    """Example 1: List all available configurations."""
    print("=== Example 1: Available Configurations ===")
    
    configs = list_available_configs()
    print(f"Found {len(configs)} predefined configurations:")
    
    for config_name in configs:
        try:
            config = get_config(config_name)
            print(f"\n{config_name}:")
            print(f"  Name: {config.name}")
            print(f"  Total Area: {config.total_area_sqm}m²")
            print(f"  Rooms: {len(config.room_requirements)}")
            print(f"  Style: {config.design_style}")
        except Exception as e:
            print(f"  Error loading {config_name}: {e}")


def example_2_generate_prompts():
    """Example 2: Generate prompts for different configurations."""
    print("\n=== Example 2: Prompt Generation ===")
    
    builder = FloorPlanPromptBuilder()
    
    # Test with 1BR apartment
    config = get_config("1br_apartment")
    
    print(f"\nConfiguration: {config.name}")
    
    # Text-to-image prompt
    text_prompt = builder.build_text_to_image_prompt(config)
    print(f"\nText-to-Image Prompt:")
    print(f"  {text_prompt}")
    
    # Image-to-image prompt
    image_prompt = builder.build_image_to_image_prompt(config)
    print(f"\nImage-to-Image Prompt:")
    print(f"  {image_prompt}")
    
    # Negative prompt
    negative_prompt = builder.build_negative_prompt()
    print(f"\nNegative Prompt:")
    print(f"  {negative_prompt}")


def example_3_prompt_variations():
    """Example 3: Generate prompt variations for testing."""
    print("\n=== Example 3: Prompt Variations ===")
    
    builder = FloorPlanPromptBuilder()
    variation_generator = PromptVariationGenerator(builder)
    
    config = get_config("2br_apartment")
    variations = variation_generator.generate_variations(config, num_variations=3)
    
    print(f"\nGenerated {len(variations)} variations for {config.name}:")
    
    for i, variation in enumerate(variations):
        print(f"\nVariation {i+1}: {variation['name']}")
        print(f"  Description: {variation['description']}")
        print(f"  Prompt: {variation['prompt'][:100]}...")


def example_4_dry_run_generation():
    """Example 4: Dry run generation (no API calls)."""
    print("\n=== Example 4: Dry Run Generation ===")
    
    try:
        # This will show what would be generated without making API calls
        generator = FloorPlanGenerator()
        
        config = get_config("3br_house")
        print(f"\nDry run for: {config.name}")
        
        # Build the prompt that would be used
        prompt = generator.prompt_builder.build_text_to_image_prompt(config)
        negative_prompt = generator.prompt_builder.build_negative_prompt()
        
        print(f"\nGenerated Prompt:")
        print(f"  {prompt}")
        
        print(f"\nNegative Prompt:")
        print(f"  {negative_prompt}")
        
        print(f"\nGeneration Settings:")
        print(f"  Image Size: {generator.generation_settings.default_image_size}")
        print(f"  Steps: {generator.generation_settings.default_steps}")
        print(f"  Guidance Scale: {generator.generation_settings.default_guidance_scale}")
        
    except Exception as e:
        print(f"Note: Could not initialize generator (likely missing API credentials): {e}")
        print("This is expected if you haven't set up your .env file yet.")


def example_5_configuration_analysis():
    """Example 5: Analyze configuration details."""
    print("\n=== Example 5: Configuration Analysis ===")
    
    config_name = "2br_apartment"
    config = get_config(config_name)
    
    print(f"\nDetailed analysis of {config_name}:")
    print(f"  Name: {config.name}")
    print(f"  Total Area: {config.total_area_sqm}m²")
    print(f"  Design Style: {config.design_style}")
    
    print(f"\nRoom Requirements ({len(config.room_requirements)} rooms):")
    total_min_area = 0
    for req in config.room_requirements:
        room_name = req.room_type.value.replace("_", " ").title()
        print(f"  {room_name}:")
        print(f"    Count: {req.count}")
        if req.min_area_sqm:
            print(f"    Min Area: {req.min_area_sqm}m²")
            total_min_area += req.min_area_sqm * req.count
        if req.constraints:
            print(f"    Constraints: {', '.join(req.constraints)}")
        if req.adjacency_preferences:
            adj_names = [adj.value.replace("_", " ").title() for adj in req.adjacency_preferences]
            print(f"    Preferred Adjacent: {', '.join(adj_names)}")
    
    print(f"\nTotal Minimum Area Required: {total_min_area}m²")
    if config.total_area_sqm:
        efficiency = (total_min_area / config.total_area_sqm) * 100
        print(f"Space Efficiency: {efficiency:.1f}%")
    
    print(f"\nArchitectural Constraints ({len(config.architectural_constraints)}):")
    for constraint in config.architectural_constraints:
        print(f"  - {constraint}")


def example_6_sample_image_setup():
    """Example 6: Set up sample images for testing."""
    print("\n=== Example 6: Sample Image Setup ===")
    
    try:
        generator = InteriorLayoutGenerator()
        sample_dir = generator.sample_images_dir
        
        print(f"Sample images directory: {sample_dir}")
        
        # Check if directory exists and create if needed
        sample_dir.mkdir(parents=True, exist_ok=True)
        
        # List existing sample images
        sample_images = generator.list_sample_images()
        
        if sample_images:
            print(f"\nFound {len(sample_images)} sample images:")
            for img_path in sample_images:
                print(f"  {img_path}")
        else:
            print(f"\nNo sample images found.")
            print(f"To test image-to-image generation, add boundary images to:")
            print(f"  {sample_dir}")
            print(f"\nSupported formats: PNG, JPEG, JPG")
            print(f"Recommended size: 1024x1024 pixels")
            
            # Create a simple instruction file
            instruction_file = sample_dir / "README.txt"
            with open(instruction_file, 'w') as f:
                f.write("Sample Images Directory\n")
                f.write("======================\n\n")
                f.write("Place your building boundary images here for testing.\n\n")
                f.write("Supported formats: PNG, JPEG, JPG\n")
                f.write("Recommended size: 1024x1024 pixels\n\n")
                f.write("Example boundary images should show:\n")
                f.write("- Building outline/perimeter\n")
                f.write("- Structural walls or boundaries\n")
                f.write("- Clear definition of interior space\n")
            
            print(f"\nCreated instruction file: {instruction_file}")
    
    except Exception as e:
        print(f"Error setting up sample images: {e}")


def main():
    """Run all examples."""
    print("Floor Plan Generation Framework - Basic Usage Examples")
    print("=" * 60)
    
    try:
        example_1_list_configurations()
        example_2_generate_prompts()
        example_3_prompt_variations()
        example_4_dry_run_generation()
        example_5_configuration_analysis()
        example_6_sample_image_setup()
        
        print("\n" + "=" * 60)
        print("Examples completed successfully!")
        print("\nNext steps:")
        print("1. Set up your .env file with Alibaba API credentials")
        print("2. Add sample boundary images to the sample_images directory")
        print("3. Try running the actual generation scripts:")
        print("   - text-to-floorplan --config 1br_apartment")
        print("   - text-image-to-floorplan --config 2br_apartment --input-image boundary.png")
        
    except Exception as e:
        print(f"\nError running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
