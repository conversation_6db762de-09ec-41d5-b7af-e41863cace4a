#!/usr/bin/env python3
"""
Testing framework for comprehensive floor plan generation evaluation.

This script provides utilities for systematic testing of the floor plan generation
capabilities across different configurations and parameters.
"""

import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add the parent directory to the path so we can import the package
sys.path.insert(0, str(Path(__file__).parent.parent))

from floorplan_invest.config import get_config, list_available_configs, FloorPlanConfig
from floorplan_invest.text_to_floorplan import FloorPlanGenerator
from floorplan_invest.text_image_to_floorplan import InteriorLayoutGenerator
from floorplan_invest.prompt_engineering import FloorPlanPromptBuilder, PromptVariationGenerator


class FloorPlanTestSuite:
    """Comprehensive testing suite for floor plan generation."""
    
    def __init__(self, output_dir: str = "./test_results"):
        """Initialize the test suite."""
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.test_results = {
            "test_session_id": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "start_time": datetime.now().isoformat(),
            "tests": [],
            "summary": {
                "total_tests": 0,
                "successful": 0,
                "failed": 0,
                "configurations_tested": set(),
                "test_types": set()
            }
        }
    
    def test_prompt_generation(self) -> Dict[str, Any]:
        """Test prompt generation for all configurations."""
        print("Testing prompt generation...")
        
        test_result = {
            "test_name": "prompt_generation",
            "test_type": "prompt_testing",
            "start_time": datetime.now().isoformat(),
            "results": [],
            "success": True,
            "error": None
        }
        
        try:
            builder = FloorPlanPromptBuilder()
            configs = list_available_configs()
            
            for config_name in configs:
                try:
                    config = get_config(config_name)
                    
                    # Test text-to-image prompt
                    text_prompt = builder.build_text_to_image_prompt(config)
                    
                    # Test image-to-image prompt
                    image_prompt = builder.build_image_to_image_prompt(config)
                    
                    # Test negative prompt
                    negative_prompt = builder.build_negative_prompt()
                    
                    config_result = {
                        "config_name": config_name,
                        "success": True,
                        "text_prompt_length": len(text_prompt),
                        "image_prompt_length": len(image_prompt),
                        "negative_prompt_length": len(negative_prompt),
                        "text_prompt": text_prompt,
                        "image_prompt": image_prompt,
                        "negative_prompt": negative_prompt
                    }
                    
                    test_result["results"].append(config_result)
                    self.test_results["summary"]["configurations_tested"].add(config_name)
                    
                except Exception as e:
                    config_result = {
                        "config_name": config_name,
                        "success": False,
                        "error": str(e)
                    }
                    test_result["results"].append(config_result)
                    test_result["success"] = False
            
        except Exception as e:
            test_result["success"] = False
            test_result["error"] = str(e)
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results["tests"].append(test_result)
        self.test_results["summary"]["test_types"].add("prompt_testing")
        
        return test_result
    
    def test_prompt_variations(self, num_variations: int = 3) -> Dict[str, Any]:
        """Test prompt variation generation."""
        print(f"Testing prompt variations ({num_variations} per config)...")
        
        test_result = {
            "test_name": "prompt_variations",
            "test_type": "variation_testing",
            "start_time": datetime.now().isoformat(),
            "num_variations": num_variations,
            "results": [],
            "success": True,
            "error": None
        }
        
        try:
            builder = FloorPlanPromptBuilder()
            variation_generator = PromptVariationGenerator(builder)
            
            # Test with a subset of configurations
            test_configs = ["1br_apartment", "2br_apartment", "3br_house"]
            
            for config_name in test_configs:
                try:
                    config = get_config(config_name)
                    variations = variation_generator.generate_variations(config, num_variations)
                    
                    config_result = {
                        "config_name": config_name,
                        "success": True,
                        "variations_generated": len(variations),
                        "variations": []
                    }
                    
                    for i, variation in enumerate(variations):
                        var_info = {
                            "variation_index": i,
                            "name": variation["name"],
                            "description": variation["description"],
                            "prompt_length": len(variation["prompt"]),
                            "prompt": variation["prompt"]
                        }
                        config_result["variations"].append(var_info)
                    
                    test_result["results"].append(config_result)
                    self.test_results["summary"]["configurations_tested"].add(config_name)
                    
                except Exception as e:
                    config_result = {
                        "config_name": config_name,
                        "success": False,
                        "error": str(e)
                    }
                    test_result["results"].append(config_result)
                    test_result["success"] = False
            
        except Exception as e:
            test_result["success"] = False
            test_result["error"] = str(e)
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results["tests"].append(test_result)
        self.test_results["summary"]["test_types"].add("variation_testing")
        
        return test_result
    
    def test_configuration_validation(self) -> Dict[str, Any]:
        """Test configuration loading and validation."""
        print("Testing configuration validation...")
        
        test_result = {
            "test_name": "configuration_validation",
            "test_type": "config_testing",
            "start_time": datetime.now().isoformat(),
            "results": [],
            "success": True,
            "error": None
        }
        
        try:
            configs = list_available_configs()
            
            for config_name in configs:
                try:
                    config = get_config(config_name)
                    
                    # Validate configuration structure
                    validation_result = self._validate_config_structure(config)
                    
                    config_result = {
                        "config_name": config_name,
                        "success": True,
                        "validation": validation_result,
                        "room_count": len(config.room_requirements),
                        "total_area": config.total_area_sqm,
                        "design_style": config.design_style,
                        "constraint_count": len(config.architectural_constraints)
                    }
                    
                    test_result["results"].append(config_result)
                    self.test_results["summary"]["configurations_tested"].add(config_name)
                    
                except Exception as e:
                    config_result = {
                        "config_name": config_name,
                        "success": False,
                        "error": str(e)
                    }
                    test_result["results"].append(config_result)
                    test_result["success"] = False
            
        except Exception as e:
            test_result["success"] = False
            test_result["error"] = str(e)
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results["tests"].append(test_result)
        self.test_results["summary"]["test_types"].add("config_testing")
        
        return test_result
    
    def test_api_client_initialization(self) -> Dict[str, Any]:
        """Test API client initialization (without making actual API calls)."""
        print("Testing API client initialization...")
        
        test_result = {
            "test_name": "api_client_initialization",
            "test_type": "api_testing",
            "start_time": datetime.now().isoformat(),
            "results": [],
            "success": True,
            "error": None
        }
        
        try:
            # Test text-to-image generator
            try:
                text_generator = FloorPlanGenerator()
                test_result["results"].append({
                    "component": "FloorPlanGenerator",
                    "success": True,
                    "api_endpoint": text_generator.api_settings.alibaba_endpoint,
                    "model_name": text_generator.api_settings.alibaba_model_name
                })
            except Exception as e:
                test_result["results"].append({
                    "component": "FloorPlanGenerator",
                    "success": False,
                    "error": str(e)
                })
                test_result["success"] = False
            
            # Test image-to-image generator
            try:
                image_generator = InteriorLayoutGenerator()
                test_result["results"].append({
                    "component": "InteriorLayoutGenerator",
                    "success": True,
                    "output_dir": str(image_generator.output_dir),
                    "sample_dir": str(image_generator.sample_images_dir)
                })
            except Exception as e:
                test_result["results"].append({
                    "component": "InteriorLayoutGenerator",
                    "success": False,
                    "error": str(e)
                })
                test_result["success"] = False
            
        except Exception as e:
            test_result["success"] = False
            test_result["error"] = str(e)
        
        test_result["end_time"] = datetime.now().isoformat()
        self.test_results["tests"].append(test_result)
        self.test_results["summary"]["test_types"].add("api_testing")
        
        return test_result
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run all tests in the suite."""
        print("Running comprehensive floor plan generation test suite...")
        print("=" * 60)
        
        # Run all tests
        self.test_configuration_validation()
        self.test_prompt_generation()
        self.test_prompt_variations()
        self.test_api_client_initialization()
        
        # Calculate summary statistics
        self.test_results["end_time"] = datetime.now().isoformat()
        self.test_results["summary"]["total_tests"] = len(self.test_results["tests"])
        
        for test in self.test_results["tests"]:
            if test["success"]:
                self.test_results["summary"]["successful"] += 1
            else:
                self.test_results["summary"]["failed"] += 1
        
        # Convert sets to lists for JSON serialization
        self.test_results["summary"]["configurations_tested"] = list(
            self.test_results["summary"]["configurations_tested"]
        )
        self.test_results["summary"]["test_types"] = list(
            self.test_results["summary"]["test_types"]
        )
        
        # Save results
        results_file = self.output_dir / f"test_results_{self.test_results['test_session_id']}.json"
        with open(results_file, 'w') as f:
            json.dump(self.test_results, f, indent=2)
        
        print(f"\nTest suite completed!")
        print(f"Results saved to: {results_file}")
        print(f"Total tests: {self.test_results['summary']['total_tests']}")
        print(f"Successful: {self.test_results['summary']['successful']}")
        print(f"Failed: {self.test_results['summary']['failed']}")
        
        return self.test_results
    
    def _validate_config_structure(self, config: FloorPlanConfig) -> Dict[str, Any]:
        """Validate the structure of a floor plan configuration."""
        validation = {
            "has_name": bool(config.name),
            "has_room_requirements": len(config.room_requirements) > 0,
            "has_total_area": config.total_area_sqm is not None,
            "has_constraints": len(config.architectural_constraints) > 0,
            "room_types": [],
            "total_rooms": 0,
            "issues": []
        }
        
        # Analyze room requirements
        room_counts = {}
        for req in config.room_requirements:
            room_type = req.room_type.value
            validation["room_types"].append(room_type)
            validation["total_rooms"] += req.count
            
            if room_type in room_counts:
                validation["issues"].append(f"Duplicate room type: {room_type}")
            room_counts[room_type] = req.count
            
            if req.count <= 0:
                validation["issues"].append(f"Invalid room count for {room_type}: {req.count}")
        
        # Check for essential rooms
        essential_rooms = ["bedroom", "bathroom", "kitchen"]
        for essential in essential_rooms:
            if essential not in room_counts:
                validation["issues"].append(f"Missing essential room type: {essential}")
        
        validation["is_valid"] = len(validation["issues"]) == 0
        
        return validation


def main():
    """Run the testing framework."""
    print("Floor Plan Generation Testing Framework")
    print("=" * 50)
    
    # Create test suite
    test_suite = FloorPlanTestSuite()
    
    # Run comprehensive tests
    results = test_suite.run_comprehensive_test()
    
    # Print summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    for test in results["tests"]:
        status = "✓ PASS" if test["success"] else "✗ FAIL"
        print(f"{status} {test['test_name']} ({test['test_type']})")
        if not test["success"] and test.get("error"):
            print(f"      Error: {test['error']}")
    
    print(f"\nOverall Results:")
    print(f"  Total Tests: {results['summary']['total_tests']}")
    print(f"  Successful: {results['summary']['successful']}")
    print(f"  Failed: {results['summary']['failed']}")
    print(f"  Configurations Tested: {len(results['summary']['configurations_tested'])}")
    
    if results['summary']['failed'] == 0:
        print("\n🎉 All tests passed! The framework is ready for use.")
    else:
        print(f"\n⚠️  {results['summary']['failed']} test(s) failed. Check the detailed results.")
    
    print(f"\nDetailed results saved to: test_results/test_results_{results['test_session_id']}.json")


if __name__ == "__main__":
    main()
