#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON><PERSON> vs Custom Implementation Comparison

This script demonstrates the differences between the original custom API client
and the new LangChain-based implementation, highlighting the benefits of using
LangChain's standardized patterns.
"""

import sys
import asyncio
from pathlib import Path
from typing import Dict, Any

# Add the parent directory to the path so we can import the package
sys.path.insert(0, str(Path(__file__).parent.parent))

from floorplan_invest.api_client import AlibabaDashScopeImageGenerator, AlibabaAPIClient
from floorplan_invest.config import get_config
from langchain_core.callbacks import BaseCallbackHandler


class FloorPlanGenerationCallback(BaseCallbackHandler):
    """Custom callback handler for monitoring floor plan generation."""
    
    def __init__(self):
        self.steps = []
        self.start_time = None
        self.end_time = None
    
    def on_llm_start(self, serialized: Dict[str, Any], prompts: list[str], **kwargs) -> None:
        """Called when LLM starts running."""
        import time
        self.start_time = time.time()
        print(f"🚀 Starting floor plan generation with {len(prompts)} prompt(s)")
        for i, prompt in enumerate(prompts):
            print(f"   Prompt {i+1}: {prompt[:100]}...")
    
    def on_llm_end(self, response, **kwargs) -> None:
        """Called when LLM ends running."""
        import time
        self.end_time = time.time()
        duration = self.end_time - self.start_time if self.start_time else 0
        print(f"✅ Floor plan generation completed in {duration:.2f} seconds")
        
        # Print generation results
        for i, generation_list in enumerate(response.generations):
            for j, generation in enumerate(generation_list):
                if generation.generation_info.get('error'):
                    print(f"   Result {i+1}.{j+1}: ❌ Error - {generation.generation_info['error']}")
                else:
                    print(f"   Result {i+1}.{j+1}: ✅ Success - {generation.text[:50]}...")
    
    def on_llm_error(self, error: Exception, **kwargs) -> None:
        """Called when LLM encounters an error."""
        print(f"❌ Floor plan generation failed: {error}")
    
    def on_text(self, text: str, **kwargs) -> None:
        """Called when arbitrary text is logged."""
        print(f"📝 {text}")
        self.steps.append(text)


def demonstrate_original_implementation():
    """Demonstrate the original custom implementation."""
    print("=" * 60)
    print("ORIGINAL CUSTOM IMPLEMENTATION")
    print("=" * 60)
    
    print("\n📋 Original Implementation Characteristics:")
    print("• Custom HTTP request handling with requests library")
    print("• Manual retry logic with exponential backoff")
    print("• Custom error handling and logging")
    print("• Tightly coupled authentication and request logic")
    print("• No standardized callback system")
    print("• Limited observability and monitoring")
    
    print("\n🔧 Code Structure (Before):")
    print("""
class AlibabaAPIClient:
    def __init__(self, api_settings, generation_settings):
        self.api_settings = api_settings
        self.generation_settings = generation_settings
        self._setup_logging()  # Custom logging setup
        self._validate_settings()  # Custom validation
    
    def _make_request(self, method, url, data, files=None):
        # Custom retry logic with for loop
        for attempt in range(self.generation_settings.max_retries):
            try:
                # Manual request handling
                response = requests.post(url, json=data, headers=headers)
                if response.status_code == 200:
                    return response.json()
                elif response.status_code == 429:
                    # Manual rate limit handling
                    time.sleep(2 ** attempt)
            except requests.exceptions.Timeout:
                # Custom timeout handling
                if attempt == max_retries - 1:
                    raise AlibabaAPIError("Timeout")
    
    def text_to_image(self, prompt, **kwargs):
        # Direct API call without abstraction
        data = {"model": self.model, "input": {"prompt": prompt}}
        return self._make_request("POST", self.endpoint, data)
    """)


def demonstrate_langchain_implementation():
    """Demonstrate the new LangChain-based implementation."""
    print("\n" + "=" * 60)
    print("NEW LANGCHAIN-BASED IMPLEMENTATION")
    print("=" * 60)
    
    print("\n🚀 LangChain Implementation Benefits:")
    print("• Standardized BaseLanguageModel interface")
    print("• Built-in retry mechanisms with tenacity")
    print("• Async/await support for better performance")
    print("• Callback system for monitoring and observability")
    print("• Pydantic models for configuration validation")
    print("• Environment variable integration")
    print("• Consistent error handling patterns")
    print("• Better separation of concerns")
    
    print("\n🏗️ Code Structure (After):")
    print("""
class AlibabaDashScopeImageGenerator(BaseLanguageModel):
    # Pydantic fields with validation
    alibaba_api_key: str = Field(..., description="API key")
    alibaba_api_secret: str = Field(..., description="API secret")
    
    @root_validator(pre=True)
    def validate_environment(cls, values):
        # Automatic environment variable loading
        values["alibaba_api_key"] = get_from_dict_or_env(
            values, "alibaba_api_key", "ALIBABA_API_KEY"
        )
        return values
    
    @retry(  # Tenacity-based retry decorator
        retry=retry_if_exception_type(httpx.HTTPError),
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def _make_async_request(self, method, url, data, run_manager=None):
        # Async request with callback support
        if run_manager:
            run_manager.on_text(f"Making {method} request")
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=data)
            return response.json()
    
    def _generate(self, prompts, run_manager=None, **kwargs):
        # LangChain standard interface
        generations = []
        for prompt in prompts:
            result = self.text_to_image(prompt, run_manager=run_manager)
            generations.append([Generation(text=result["image_url"])])
        return LLMResult(generations=generations)
    """)


async def demonstrate_langchain_features():
    """Demonstrate LangChain-specific features."""
    print("\n" + "=" * 60)
    print("LANGCHAIN FEATURES DEMONSTRATION")
    print("=" * 60)
    
    try:
        # Create callback handler
        callback = FloorPlanGenerationCallback()
        
        # Initialize LangChain-based client
        print("\n🔧 Initializing LangChain-based client...")
        client = AlibabaDashScopeImageGenerator(
            alibaba_api_key="demo_key",  # This will fail, but shows the pattern
            alibaba_api_secret="demo_secret",
            callbacks=[callback]
        )
        
        print("✅ Client initialized with:")
        print(f"   • Model: {client.model_name}")
        print(f"   • Endpoint: {client.alibaba_endpoint}")
        print(f"   • Default Image Size: {client.default_image_size}")
        print(f"   • Max Retries: {client.max_retries}")
        print(f"   • Callbacks: {len(client.callbacks) if hasattr(client, 'callbacks') else 0}")
        
        # Demonstrate LangChain interface
        print("\n🎯 LangChain Standard Interface:")
        config = get_config("1br_apartment")
        prompt = f"Architectural floor plan for {config.name}"
        
        print(f"   • Using _generate() method with prompt: {prompt[:50]}...")
        print("   • This would call the standard LangChain interface")
        print("   • Callbacks would track each step of the process")
        print("   • Retry logic would handle failures automatically")
        print("   • Async operations would improve performance")
        
    except Exception as e:
        print(f"   ⚠️  Demo client creation failed (expected): {e}")
        print("   💡 This is expected without real API credentials")


def demonstrate_backward_compatibility():
    """Demonstrate backward compatibility wrapper."""
    print("\n" + "=" * 60)
    print("BACKWARD COMPATIBILITY")
    print("=" * 60)
    
    print("\n🔄 Backward Compatibility Features:")
    print("• AlibabaAPIClient wrapper maintains original interface")
    print("• Existing code continues to work without changes")
    print("• Gradual migration path to LangChain patterns")
    print("• All original methods preserved")
    
    print("\n📝 Migration Example:")
    print("""
# Original code (still works):
from floorplan_invest.api_client import AlibabaAPIClient
client = AlibabaAPIClient(api_settings, generation_settings)
result = client.text_to_image("floor plan prompt")

# New LangChain code (enhanced features):
from floorplan_invest.api_client import AlibabaDashScopeImageGenerator
from langchain_core.callbacks import CallbackManager

callback_manager = CallbackManager([FloorPlanGenerationCallback()])
client = AlibabaDashScopeImageGenerator(
    alibaba_api_key="your_key",
    callbacks=[callback_manager]
)
result = client._generate(["floor plan prompt"])
    """)


def compare_error_handling():
    """Compare error handling approaches."""
    print("\n" + "=" * 60)
    print("ERROR HANDLING COMPARISON")
    print("=" * 60)
    
    print("\n❌ Original Error Handling:")
    print("""
try:
    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=data)
            if response.status_code == 429:
                time.sleep(2 ** attempt)  # Manual backoff
                continue
            elif response.status_code != 200:
                raise AlibabaAPIError(f"Status {response.status_code}")
            return response.json()
        except requests.exceptions.Timeout:
            if attempt == max_retries - 1:
                raise AlibabaAPIError("Timeout after retries")
except Exception as e:
    logger.error(f"Request failed: {e}")
    raise
    """)
    
    print("\n✅ LangChain Error Handling:")
    print("""
@retry(
    retry=retry_if_exception_type((httpx.HTTPError, httpx.TimeoutException)),
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10),
    reraise=True,
)
async def _make_async_request(self, method, url, data, run_manager=None):
    if run_manager:
        run_manager.on_text(f"Making {method} request")
    
    async with httpx.AsyncClient(timeout=self.timeout_seconds) as client:
        try:
            response = await client.post(url, json=data)
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            if run_manager:
                run_manager.on_text(f"Request failed: {e}")
            raise AlibabaAPIError(f"API error: {e}")
    """)


def main():
    """Run the comprehensive comparison."""
    print("🏗️  LangChain vs Custom Implementation Comparison")
    print("=" * 80)
    
    demonstrate_original_implementation()
    demonstrate_langchain_implementation()
    
    # Run async demonstration
    asyncio.run(demonstrate_langchain_features())
    
    demonstrate_backward_compatibility()
    compare_error_handling()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY OF BENEFITS")
    print("=" * 80)
    
    benefits = [
        "🎯 Standardized Interface: Follows LangChain's BaseLanguageModel pattern",
        "🔄 Retry Logic: Built-in tenacity-based retry with exponential backoff",
        "📊 Observability: Callback system for monitoring and debugging",
        "⚡ Performance: Async/await support for better concurrency",
        "🛡️ Validation: Pydantic models for configuration validation",
        "🌍 Environment: Automatic environment variable integration",
        "🔧 Maintainability: Better separation of concerns and modularity",
        "🔄 Compatibility: Backward compatibility wrapper for gradual migration",
        "📈 Scalability: Better suited for production deployments",
        "🧪 Testing: Easier to mock and test with LangChain patterns"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print("\n💡 Next Steps:")
    print("  1. Update your code to use the new LangChain-based client")
    print("  2. Add callback handlers for monitoring and debugging")
    print("  3. Leverage async capabilities for better performance")
    print("  4. Use LangChain's ecosystem for additional features")
    print("  5. Consider migrating to LangChain's standard patterns")


if __name__ == "__main__":
    main()
