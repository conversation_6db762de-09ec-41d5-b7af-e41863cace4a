# Alibaba Cloud API Configuration
# ================================
# Get your API credentials from Alibaba Cloud DashScope console
# https://dashscope.console.aliyun.com/

# Required: Your Alibaba Cloud API Key
ALIBABA_API_KEY=sk-49aa56a9edde41df81f819c314d752c4

# Required: Your Alibaba Cloud API Secret
ALIBABA_API_SECRET=sk-49aa56a9edde41df81f819c314d752c4

# Optional: API Endpoint (default is provided)
ALIBABA_ENDPOINT=https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis

# Optional: Model Name (default is wanx-v1)
ALIBABA_MODEL_NAME=wanx-v1

# Generation Settings
# ===================

# Output directory for generated images
OUTPUT_DIR=./outputs

# Directory for sample input images
SAMPLE_IMAGES_DIR=./sample_images

# Default image size (e.g., "1024x1024", "512x512")
DEFAULT_IMAGE_SIZE=1024*1024

# Number of generation steps (1-100, higher = better quality but slower)
DEFAULT_STEPS=20

# Guidance scale (1.0-20.0, higher = more adherence to prompt)
DEFAULT_GUIDANCE_SCALE=7.5

# Maximum number of retries for failed requests
MAX_RETRIES=3

# Request timeout in seconds
TIMEOUT_SECONDS=60

# Logging Configuration
# ====================

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Log file path
LOG_FILE=./logs/floorplan_generation.log

# Example Configuration
# ====================
# Copy this file to .env and fill in your actual values:
# cp .env.example .env
# 
# Then edit .env with your actual Alibaba Cloud credentials
