{"test_session_id": "20250803_162417", "start_time": "2025-08-03T16:24:17.611709", "tests": [{"test_name": "configuration_validation", "test_type": "config_testing", "start_time": "2025-08-03T16:24:17.611714", "results": [{"config_name": "1br_apartment", "success": true, "validation": {"has_name": true, "has_room_requirements": true, "has_total_area": true, "has_constraints": true, "room_types": ["bedroom", "bathroom", "kitchen", "living_room", "balcony", "entrance"], "total_rooms": 6, "issues": [], "is_valid": true}, "room_count": 6, "total_area": 50.0, "design_style": "modern minimalist", "constraint_count": 6}, {"config_name": "2br_apartment", "success": true, "validation": {"has_name": true, "has_room_requirements": true, "has_total_area": true, "has_constraints": true, "room_types": ["bedroom", "bathroom", "kitchen", "living_room", "dining_room", "balcony", "entrance"], "total_rooms": 8, "issues": [], "is_valid": true}, "room_count": 7, "total_area": 80.0, "design_style": "contemporary", "constraint_count": 6}, {"config_name": "3br_house", "success": true, "validation": {"has_name": true, "has_room_requirements": true, "has_total_area": true, "has_constraints": true, "room_types": ["bedroom", "bathroom", "kitchen", "living_room", "dining_room", "study", "storage", "entrance"], "total_rooms": 11, "issues": [], "is_valid": true}, "room_count": 8, "total_area": 120.0, "design_style": "traditional family home", "constraint_count": 7}], "success": true, "error": null, "end_time": "2025-08-03T16:24:17.611749"}, {"test_name": "prompt_generation", "test_type": "prompt_testing", "start_time": "2025-08-03T16:24:17.611755", "results": [{"config_name": "1br_apartment", "success": true, "text_prompt_length": 1020, "image_prompt_length": 920, "negative_prompt_length": 289, "text_prompt": "Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern minimalist style with modern minimalist. following these design principles: efficient use of space; good natural lighting; proper ventilation; clear circulation paths; bathroom should be accessible from main living area; bedroom should have privacy from main entrance. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). drawn as professional architectural floor plan; top-down view with clear room boundaries; proper scale and proportions; clean line work and clear spatial relationships; standard architectural drawing conventions.", "image_prompt": "Interior floor plan layout for a 1 bedroom apartment. within the given building outline. organizing these spaces: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. optimizing the interior layout for functionality and flow. following these principles: efficient use of space; good natural lighting; proper ventilation; clear circulation paths; bathroom should be accessible from main living area; bedroom should have privacy from main entrance. ensuring: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). respecting the existing building boundary and structural constraints. with proper door placements, circulation paths, and room connections.", "negative_prompt": "blurry, low quality, distorted, incomplete, multiple toilets in one bathroom, overlapping rooms, impossible layouts, structural impossibilities, missing doors, inaccessible rooms, poor circulation, unrealistic proportions, cluttered, confusing layout, text, labels, annotations, watermarks"}, {"config_name": "2br_apartment", "success": true, "text_prompt_length": 1097, "image_prompt_length": 964, "negative_prompt_length": 289, "text_prompt": "Architectural floor plan drawing of a 2 bedroom apartment. with total area of 80.0 square meters. containing: 2 bedrooms, 1 bathroom, 1 kitchen, 1 living room, 1 dining room, 1 balcony, 1 entrance. designed in contemporary style with current design trends, mixed materials, flexible spaces. following these design principles: separate bedrooms for privacy; master bedroom should be larger; kitchen should connect to dining area; good natural lighting in all rooms; proper ventilation throughout; clear separation between public and private areas. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 10.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). drawn as professional architectural floor plan; top-down view with clear room boundaries; proper scale and proportions; clean line work and clear spatial relationships; standard architectural drawing conventions.", "image_prompt": "Interior floor plan layout for a 2 bedroom apartment. within the given building outline. organizing these spaces: 2 bedrooms, 1 bathroom, 1 kitchen, 1 living room, 1 dining room, 1 balcony, 1 entrance. optimizing the interior layout for functionality and flow. following these principles: separate bedrooms for privacy; master bedroom should be larger; kitchen should connect to dining area; good natural lighting in all rooms; proper ventilation throughout; clear separation between public and private areas. ensuring: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 10.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). respecting the existing building boundary and structural constraints. with proper door placements, circulation paths, and room connections.", "negative_prompt": "blurry, low quality, distorted, incomplete, multiple toilets in one bathroom, overlapping rooms, impossible layouts, structural impossibilities, missing doors, inaccessible rooms, poor circulation, unrealistic proportions, cluttered, confusing layout, text, labels, annotations, watermarks"}, {"config_name": "3br_house", "success": true, "text_prompt_length": 1133, "image_prompt_length": 1020, "negative_prompt_length": 289, "text_prompt": "Architectural floor plan drawing of a 3 bedroom house. with total area of 120.0 square meters. containing: 3 bedrooms, 2 bathrooms, 1 kitchen, 1 living room, 1 dining room, 1 study, 1 storage, 1 entrance. designed in traditional family home style with traditional family home. following these design principles: master bedroom should have en-suite bathroom; family bathroom accessible from hallway; kitchen should have good workflow triangle; living and dining areas should flow together; study should be quiet and separate from main living areas; good storage throughout the house. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). drawn as professional architectural floor plan; top-down view with clear room boundaries; proper scale and proportions; clean line work and clear spatial relationships; standard architectural drawing conventions.", "image_prompt": "Interior floor plan layout for a 3 bedroom house. within the given building outline. organizing these spaces: 3 bedrooms, 2 bathrooms, 1 kitchen, 1 living room, 1 dining room, 1 study, 1 storage, 1 entrance. optimizing the interior layout for functionality and flow. following these principles: master bedroom should have en-suite bathroom; family bathroom accessible from hallway; kitchen should have good workflow triangle; living and dining areas should flow together; study should be quiet and separate from main living areas; good storage throughout the house. ensuring: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). respecting the existing building boundary and structural constraints. with proper door placements, circulation paths, and room connections.", "negative_prompt": "blurry, low quality, distorted, incomplete, multiple toilets in one bathroom, overlapping rooms, impossible layouts, structural impossibilities, missing doors, inaccessible rooms, poor circulation, unrealistic proportions, cluttered, confusing layout, text, labels, annotations, watermarks"}], "success": true, "error": null, "end_time": "2025-08-03T16:24:17.611902"}, {"test_name": "prompt_variations", "test_type": "variation_testing", "start_time": "2025-08-03T16:24:17.611906", "num_variations": 3, "results": [{"config_name": "1br_apartment", "success": true, "variations_generated": 3, "variations": [{"variation_index": 0, "name": "1 Bedroom Apartment_variation_1", "description": "Technical detailed version with full specifications", "prompt_length": 1020, "prompt": "Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern minimalist style with modern minimalist. following these design principles: efficient use of space; good natural lighting; proper ventilation; clear circulation paths; bathroom should be accessible from main living area; bedroom should have privacy from main entrance. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). drawn as professional architectural floor plan; top-down view with clear room boundaries; proper scale and proportions; clean line work and clear spatial relationships; standard architectural drawing conventions."}, {"variation_index": 1, "name": "1 Bedroom Apartment_variation_2", "description": "Simplified aesthetic version focusing on design", "prompt_length": 807, "prompt": "Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern minimalist style with modern minimalist. following these design principles: efficient use of space; good natural lighting; proper ventilation; clear circulation paths; bathroom should be accessible from main living area; bedroom should have privacy from main entrance. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan)."}, {"variation_index": 2, "name": "1 Bedroom Apartment_variation_3", "description": "Enhanced version with efficiency emphasis", "prompt_length": 1079, "prompt": "Architectural floor plan drawing of a 1 bedroom apartment. with total area of 50.0 square meters. containing: 1 bedroom, 1 bathroom, 1 kitchen, 1 living room, 1 balcony, 1 entrance. designed in modern minimalist style with modern minimalist. following these design principles: efficient use of space; good natural lighting; proper ventilation; clear circulation paths; bathroom should be accessible from main living area; bedroom should have privacy from main entrance. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). drawn as professional architectural floor plan; top-down view with clear room boundaries; proper scale and proportions; clean line work and clear spatial relationships; standard architectural drawing conventions. Emphasize spatial efficiency and modern design aesthetics."}]}, {"config_name": "2br_apartment", "success": true, "variations_generated": 3, "variations": [{"variation_index": 0, "name": "2 Bedroom Apartment_variation_1", "description": "Technical detailed version with full specifications", "prompt_length": 1097, "prompt": "Architectural floor plan drawing of a 2 bedroom apartment. with total area of 80.0 square meters. containing: 2 bedrooms, 1 bathroom, 1 kitchen, 1 living room, 1 dining room, 1 balcony, 1 entrance. designed in contemporary style with current design trends, mixed materials, flexible spaces. following these design principles: separate bedrooms for privacy; master bedroom should be larger; kitchen should connect to dining area; good natural lighting in all rooms; proper ventilation throughout; clear separation between public and private areas. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 10.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). drawn as professional architectural floor plan; top-down view with clear room boundaries; proper scale and proportions; clean line work and clear spatial relationships; standard architectural drawing conventions."}, {"variation_index": 1, "name": "2 Bedroom Apartment_variation_2", "description": "Simplified aesthetic version focusing on design", "prompt_length": 884, "prompt": "Architectural floor plan drawing of a 2 bedroom apartment. with total area of 80.0 square meters. containing: 2 bedrooms, 1 bathroom, 1 kitchen, 1 living room, 1 dining room, 1 balcony, 1 entrance. designed in contemporary style with current design trends, mixed materials, flexible spaces. following these design principles: separate bedrooms for privacy; master bedroom should be larger; kitchen should connect to dining area; good natural lighting in all rooms; proper ventilation throughout; clear separation between public and private areas. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 10.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan)."}, {"variation_index": 2, "name": "2 Bedroom Apartment_variation_3", "description": "Enhanced version with efficiency emphasis", "prompt_length": 1156, "prompt": "Architectural floor plan drawing of a 2 bedroom apartment. with total area of 80.0 square meters. containing: 2 bedrooms, 1 bathroom, 1 kitchen, 1 living room, 1 dining room, 1 balcony, 1 entrance. designed in contemporary style with current design trends, mixed materials, flexible spaces. following these design principles: separate bedrooms for privacy; master bedroom should be larger; kitchen should connect to dining area; good natural lighting in all rooms; proper ventilation throughout; clear separation between public and private areas. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 10.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). drawn as professional architectural floor plan; top-down view with clear room boundaries; proper scale and proportions; clean line work and clear spatial relationships; standard architectural drawing conventions. Emphasize spatial efficiency and modern design aesthetics."}]}, {"config_name": "3br_house", "success": true, "variations_generated": 3, "variations": [{"variation_index": 0, "name": "3 Bedroom House_variation_1", "description": "Technical detailed version with full specifications", "prompt_length": 1133, "prompt": "Architectural floor plan drawing of a 3 bedroom house. with total area of 120.0 square meters. containing: 3 bedrooms, 2 bathrooms, 1 kitchen, 1 living room, 1 dining room, 1 study, 1 storage, 1 entrance. designed in traditional family home style with traditional family home. following these design principles: master bedroom should have en-suite bathroom; family bathroom accessible from hallway; kitchen should have good workflow triangle; living and dining areas should flow together; study should be quiet and separate from main living areas; good storage throughout the house. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). drawn as professional architectural floor plan; top-down view with clear room boundaries; proper scale and proportions; clean line work and clear spatial relationships; standard architectural drawing conventions."}, {"variation_index": 1, "name": "3 Bedroom House_variation_2", "description": "Simplified aesthetic version focusing on design", "prompt_length": 920, "prompt": "Architectural floor plan drawing of a 3 bedroom house. with total area of 120.0 square meters. containing: 3 bedrooms, 2 bathrooms, 1 kitchen, 1 living room, 1 dining room, 1 study, 1 storage, 1 entrance. designed in traditional family home style with traditional family home. following these design principles: master bedroom should have en-suite bathroom; family bathroom accessible from hallway; kitchen should have good workflow triangle; living and dining areas should flow together; study should be quiet and separate from main living areas; good storage throughout the house. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan)."}, {"variation_index": 2, "name": "3 Bedroom House_variation_3", "description": "Enhanced version with efficiency emphasis", "prompt_length": 1192, "prompt": "Architectural floor plan drawing of a 3 bedroom house. with total area of 120.0 square meters. containing: 3 bedrooms, 2 bathrooms, 1 kitchen, 1 living room, 1 dining room, 1 study, 1 storage, 1 entrance. designed in traditional family home style with traditional family home. following these design principles: master bedroom should have en-suite bathroom; family bathroom accessible from hallway; kitchen should have good workflow triangle; living and dining areas should flow together; study should be quiet and separate from main living areas; good storage throughout the house. with specific room requirements: Ensure privacy from main living areas; Provide adequate space for bed and furniture; Include closet or storage space; Ensure natural lighting and ventilation; Consider noise isolation; bedroom minimum 12.0m²; Each bathroom must contain only one toilet; Include proper ventilation (window or exhaust fan). drawn as professional architectural floor plan; top-down view with clear room boundaries; proper scale and proportions; clean line work and clear spatial relationships; standard architectural drawing conventions. Emphasize spatial efficiency and modern design aesthetics."}]}], "success": true, "error": null, "end_time": "2025-08-03T16:24:17.612080"}, {"test_name": "api_client_initialization", "test_type": "api_testing", "start_time": "2025-08-03T16:24:17.612083", "results": [{"component": "FloorPlanGenerator", "success": true, "api_endpoint": "https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis", "model_name": "wanx-v1"}, {"component": "InteriorLayoutGenerator", "success": true, "output_dir": "outputs", "sample_dir": "sample_images"}], "success": true, "error": null, "end_time": "2025-08-03T16:24:17.619019"}], "summary": {"total_tests": 4, "successful": 4, "failed": 0, "configurations_tested": ["3br_house", "2br_apartment", "1br_apartment"], "test_types": ["variation_testing", "config_testing", "prompt_testing", "api_testing"]}, "end_time": "2025-08-03T16:24:17.619027"}