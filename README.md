# Floor Plan Investment - AI Floor Plan Generation Framework

A comprehensive testing framework for generating architectural floor plans using Alibaba's AI models. This project provides tools for both text-to-image and text+image-to-image floor plan generation with extensive configuration options and testing capabilities.

## Features

- **Dual Generation Modes**: Support for both text-to-image and text+image-to-image generation
- **Comprehensive Configuration System**: Pre-built configurations for various apartment and house layouts
- **Advanced Prompt Engineering**: Sophisticated prompt generation with architectural constraints
- **Batch Processing**: Generate multiple floor plans with different configurations
- **Variation Testing**: Generate multiple variations of the same floor plan for comparison
- **Extensive Testing Framework**: Built-in testing utilities for systematic evaluation
- **Error Handling & Retry Logic**: Robust API interaction with automatic retries
- **Flexible Output Management**: Organized output structure with detailed logging

## Project Structure

```
floorplan_invest/
├── floorplan_invest/           # Main package
│   ├── __init__.py
│   ├── config.py              # Configuration system
│   ├── api_client.py          # Alibaba API client
│   ├── prompt_engineering.py  # Prompt generation utilities
│   ├── text_to_floorplan.py   # Text-to-image generation
│   └── text_image_to_floorplan.py  # Text+image-to-image generation
├── examples/                   # Usage examples and testing
│   ├── basic_usage.py         # Basic usage examples
│   └── testing_framework.py   # Comprehensive testing suite
├── outputs/                    # Generated floor plans
├── sample_images/             # Sample boundary images
├── logs/                      # Application logs
├── .env.example               # Environment configuration template
├── pyproject.toml            # Project configuration
└── README.md                 # This file
```

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd floorplan_invest
   ```

2. **Set up Python environment with uv**:
   ```bash
   # Create virtual environment
   uv venv

   # Activate environment
   source .venv/bin/activate

   # Install dependencies
   uv pip install -e .
   ```

3. **Configure API credentials**:
   ```bash
   # Copy environment template
   cp .env.example .env

   # Edit .env with your Alibaba Cloud credentials
   # Get credentials from: https://dashscope.console.aliyun.com/
   ```

## Quick Start

### 1. List Available Configurations

```bash
# See all predefined floor plan configurations
text-to-floorplan --list-configs
```

### 2. Generate a Single Floor Plan

```bash
# Generate a 1-bedroom apartment floor plan
text-to-floorplan --config 1br_apartment

# Generate with multiple variations
text-to-floorplan --config 2br_apartment --variations 3
```

### 3. Generate Interior Layout from Boundary Image

```bash
# First, add a boundary image to sample_images/
# Then generate interior layout
text-image-to-floorplan --config 2br_apartment --input-image sample_images/boundary.png

# Test different transformation strengths
text-image-to-floorplan --config 2br_apartment --input-image boundary.png --test-strengths
```

### 4. Batch Generation

```bash
# Generate multiple configurations at once
text-to-floorplan --batch "1br_apartment,2br_apartment,3br_house"

# Batch interior generation
text-image-to-floorplan --batch-configs "1br_apartment,2br_apartment" --batch-images "boundary1.png,boundary2.png"
```

## Configuration System

The framework includes predefined configurations for common residential layouts:

- **1br_apartment**: 1-bedroom apartment (~50m²)
- **2br_apartment**: 2-bedroom apartment (~80m²)
- **3br_house**: 3-bedroom house (~120m²)

Each configuration includes:
- Room requirements with counts and minimum areas
- Architectural constraints and design principles
- Room-specific requirements (e.g., "each bathroom must contain only one toilet")
- Adjacency preferences between rooms
- Design style preferences

### Custom Configurations

You can create custom configurations programmatically:

```python
from floorplan_invest.config import create_custom_config, RoomRequirement, RoomType

custom_config = create_custom_config(
    name="Custom Studio",
    room_requirements=[
        RoomRequirement(room_type=RoomType.BEDROOM, count=1, min_area_sqm=20.0),
        RoomRequirement(room_type=RoomType.BATHROOM, count=1, min_area_sqm=5.0),
        RoomRequirement(room_type=RoomType.KITCHEN, count=1, min_area_sqm=8.0),
    ],
    total_area_sqm=40.0,
    design_style="modern minimalist"
)
```

## Advanced Usage

### Prompt Engineering

The framework includes sophisticated prompt engineering capabilities:

```python
from floorplan_invest.prompt_engineering import FloorPlanPromptBuilder
from floorplan_invest.config import get_config

builder = FloorPlanPromptBuilder()
config = get_config("2br_apartment")

# Generate text-to-image prompt
text_prompt = builder.build_text_to_image_prompt(config)

# Generate image-to-image prompt
image_prompt = builder.build_image_to_image_prompt(config)

# Generate negative prompt
negative_prompt = builder.build_negative_prompt()
```

### Testing Framework

Run comprehensive tests to evaluate the framework:

```python
# Run basic usage examples
python examples/basic_usage.py

# Run comprehensive testing suite
python examples/testing_framework.py
```

### Dry Run Mode

Test prompts without making API calls:

```bash
# See what prompts would be generated
text-to-floorplan --config 1br_apartment --dry-run

# Test image-to-image prompts
text-image-to-floorplan --config 2br_apartment --input-image boundary.png --dry-run
```

## API Configuration

The framework supports extensive API configuration through environment variables:

```bash
# Core API settings
ALIBABA_API_KEY=your_api_key
ALIBABA_API_SECRET=your_api_secret
ALIBABA_ENDPOINT=https://dashscope.aliyuncs.com/api/v1/services/aigc/text2image/image-synthesis
ALIBABA_MODEL_NAME=wanx-v1

# Generation parameters
DEFAULT_IMAGE_SIZE=1024x1024
DEFAULT_STEPS=20
DEFAULT_GUIDANCE_SCALE=7.5

# Reliability settings
MAX_RETRIES=3
TIMEOUT_SECONDS=60
```

## Output Management

Generated images and metadata are organized in the `outputs/` directory:

```
outputs/
├── 1br_apartment_20240102_143022.png
├── 2br_apartment_variation_1.png
├── batch_results_20240102_143022.json
└── interior_batch_results_20240102_143500.json
```

Each generation includes detailed metadata:
- Configuration used
- Generated prompts
- Generation parameters
- Timestamps and success status
- Error information (if applicable)

## Testing and Evaluation

### Built-in Testing

The framework includes comprehensive testing utilities:

1. **Configuration Validation**: Verify all predefined configurations
2. **Prompt Generation Testing**: Test prompt generation for all configurations
3. **Variation Testing**: Test prompt variation generation
4. **API Client Testing**: Validate API client initialization

### Performance Testing

Test different parameters systematically:

```bash
# Test multiple variations
text-to-floorplan --config 2br_apartment --variations 5

# Test different transformation strengths
text-image-to-floorplan --config 2br_apartment --input-image boundary.png --test-strengths

# Batch testing across configurations
text-to-floorplan --batch "1br_apartment,2br_apartment,3br_house"
```

## Troubleshooting

### Common Issues

1. **API Credentials**: Ensure your `.env` file has valid Alibaba Cloud credentials
2. **Image Format**: Use PNG, JPEG, or JPG formats for input images
3. **Image Size**: Recommended input size is 1024x1024 pixels
4. **Rate Limits**: The framework includes automatic retry logic for rate limiting

### Debug Mode

Enable detailed logging:

```bash
# Set debug logging in .env
LOG_LEVEL=DEBUG

# Check logs
tail -f logs/floorplan_generation.log
```

### Validation

Validate your setup:

```python
# Run basic validation
python examples/basic_usage.py

# Run comprehensive tests
python examples/testing_framework.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

[Add your license information here]

## Support

For issues and questions:
1. Check the troubleshooting section
2. Run the testing framework to identify issues
3. Review the logs in `logs/floorplan_generation.log`
4. Open an issue with detailed error information